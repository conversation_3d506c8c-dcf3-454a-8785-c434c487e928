<?php

echo "=== TEST ULEPSZONEJ WALIDACJI WYMIARÓW ANALITYCZNYCH ===\n\n";

// Test 1: Sprawdzenie czy RequiredAccountDimensionsRule została zaktualizowana
$ruleFile = 'modules/Users/<USER>/Rules/RequiredAccountDimensionsRule.php';
if (file_exists($ruleFile)) {
    echo "✓ RequiredAccountDimensionsRule istnieje\n";
    
    $content = file_get_contents($ruleFile);
    
    if (strpos($content, 'missingRequiredDimensions') !== false) {
        echo "✓ Dodano tracking brakujących wymiarów\n";
    } else {
        echo "✗ Brak trackingu brakujących wymiarów\n";
    }
    
    if (strpos($content, 'required-account-dimensions-missing-with-names') !== false) {
        echo "✓ Dodano komunikat z nazwami wymiarów\n";
    } else {
        echo "✗ Brak komunikatu z nazwami wymiarów\n";
    }
    
    if (strpos($content, 'implode') !== false) {
        echo "✓ Dodano łączenie nazw wymiarów\n";
    } else {
        echo "✗ Brak łączenia nazw wymiarów\n";
    }
} else {
    echo "✗ RequiredAccountDimensionsRule nie istnieje\n";
}

// Test 2: Sprawdzenie komunikatów błędów
$enErrorFile = 'modules/Users/<USER>/Resources/lang/en/error.php';
$plErrorFile = 'modules/Users/<USER>/Resources/lang/pl_PL.utf8/error.php';

if (file_exists($enErrorFile)) {
    $enErrors = include $enErrorFile;
    if (isset($enErrors['required-account-dimensions-missing-with-names'])) {
        echo "✓ Komunikat z nazwami wymiarów (EN) istnieje: " . $enErrors['required-account-dimensions-missing-with-names'] . "\n";
    } else {
        echo "✗ Komunikat z nazwami wymiarów (EN) nie istnieje\n";
    }
}

if (file_exists($plErrorFile)) {
    $plErrors = include $plErrorFile;
    if (isset($plErrors['required-account-dimensions-missing-with-names'])) {
        echo "✓ Komunikat z nazwami wymiarów (PL) istnieje: " . $plErrors['required-account-dimensions-missing-with-names'] . "\n";
    } else {
        echo "✗ Komunikat z nazwami wymiarów (PL) nie istnieje\n";
    }
}

echo "\n=== PODSUMOWANIE ULEPSZEŃ ===\n";
echo "✅ Walidacja wymiarów analitycznych została ulepszona!\n\n";

echo "Nowe funkcjonalności:\n";
echo "- Komunikat błędu zawiera nazwy brakujących wymiarów analitycznych\n";
echo "- Przykład komunikatu (PL): 'Wymagane wymiary analityczne: Centrum kosztów, Projekt'\n";
echo "- Przykład komunikatu (EN): 'Required account dimensions: Cost Center, Project'\n";
echo "- Fallback do ogólnego komunikatu jeśli nie ma brakujących wymiarów\n\n";

echo "Jak to działa:\n";
echo "1. System sprawdza które wymiary analityczne są wymagane\n";
echo "2. Porównuje z podanymi przez użytkownika\n";
echo "3. Zbiera nazwy brakujących wymiarów\n";
echo "4. Wyświetla komunikat z konkretnymi nazwami wymiarów\n";
echo "5. Użytkownik dokładnie wie, które wymiary musi uzupełnić\n\n";

echo "Przykłady komunikatów:\n";
echo "- Jeśli brakuje 'Centrum kosztów': 'Wymagane wymiary analityczne: Centrum kosztów'\n";
echo "- Jeśli brakuje kilku: 'Wymagane wymiary analityczne: Centrum kosztów, Projekt, Dział'\n";
echo "- Komunikat jest w języku użytkownika (PL/EN)\n";
