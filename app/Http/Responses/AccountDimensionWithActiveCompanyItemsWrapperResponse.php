<?php

declare(strict_types=1);

namespace App\Http\Responses;

use Modules\Accounting\Pub\ViewObjects\AccountDimensionWithItemsViewObject;

class AccountDimensionWithActiveCompanyItemsWrapperResponse extends Response2
{
    protected function transform(AccountDimensionWithItemsViewObject $viewObject): array
    {
        return [
            'id' => $viewObject->getId(),
            'code' => $viewObject->getCode(),
            'name' => $viewObject->getName(),
            'slug' => $viewObject->getSlug(),
            'visibility' => $viewObject->getAccountDimensionVisibilityEnum(),
            'is_active' => $viewObject->getIsActive(),
            'is_required' => $viewObject->isRequired(),
            'disabled' => $viewObject->isDisabled(),
            'items' => $viewObject->getItems(),
        ];
    }
}
