<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Repositories;

use App\Database\TransactionManagerInterface;
use App\Repositories\Repository;
use App\Services\SlugGeneratorService;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Psr\Log\LoggerInterface;

class UserAccountDimensionItemRepository extends Repository
{
    protected TransactionManagerInterface $transactionManager;

    protected SlugGeneratorService $slugGenerator;

    protected LoggerInterface $logger;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    public function __construct(
        TransactionManagerInterface $transactionManager,
        SlugGeneratorService $slugGenerator,
        LoggerInterface $logger,
        AccountDimensionItemRepository $accountDimensionItemRepository
    ) {
        parent::__construct();
        $this->transactionManager = $transactionManager;
        $this->slugGenerator = $slugGenerator;
        $this->logger = $logger;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
    }

    public function findAllByAccountDimensionItemId(
        int $accountDimensionItemId,
        ?string $type = UserAccountDimensionItem::TYPE_DEFAULT
    ): Collection
    {
        if (!in_array($type, UserAccountDimensionItem::getAllowedTypes())) {
            throw new \InvalidArgumentException(sprintf('%s is not allowed type', $type));
        }

        $this->prepare();

        return $this->builder->where([
            'account_dimension_item_id' => $accountDimensionItemId,
            'type' => $type,
        ])->get();
    }

    public function disconnectAll(int $userId): void
    {
        $this->prepare();

        $this->builder->where([
            'user_id' => $userId
        ])->delete();
    }

    public function findAllForUser(int $userId): Collection
    {
        $this->prepare();

        return $this
            ->builder
            ->with(['accountDimension', 'accountDimensionItem'])
            ->where([
                'user_id' => $userId
            ])
            ->get();
    }

    protected function beforeCreate(Model $model, $data)
    {
        $model->slug = $this->slugGenerator->generate();

        return $model;
    }

    static function model()
    {
        return UserAccountDimensionItem::class;
    }

    public function connectWithItem(
        int $userId,
        AccountDimensionItem $accountDimensionItem,
        ?bool $removeAllPreviousDimensions = true,
        ?string $type = null
    ): UserAccountDimensionItem {
        $this->prepare();
        if (true === $removeAllPreviousDimensions) {
            $this->builder->where([
                'user_id' => $userId,
                'account_dimension_id' => $accountDimensionItem->account_dimension_id,
            ])->delete();
        } elseif(false === $removeAllPreviousDimensions) {
            $this->builder->where([
                'user_id' => $userId,
                'account_dimension_id' => $accountDimensionItem->account_dimension_id,
                'account_dimension_item_id' => $accountDimensionItem->id,
            ])->delete();
        }

        $userAccountDimensionItem = new UserAccountDimensionItem();
        $userAccountDimensionItem->user_id = $userId;
        $userAccountDimensionItem->account_dimension_item_id = $accountDimensionItem->id;
        $userAccountDimensionItem->account_dimension_id = $accountDimensionItem->account_dimension_id;
        $userAccountDimensionItem->type = $type;

        $userAccountDimensionItem = $this->persist($userAccountDimensionItem);

        return $this->getFreshModel($userAccountDimensionItem);
    }
}
