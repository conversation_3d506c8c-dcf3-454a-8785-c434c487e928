<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\UserAccountDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\UserDto;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class UserAccountDimensionItemService
{
    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected UserAccountDimensionItemRepository $userAccountDimensionItemRepository;

    protected AccountDimensionService $accountDimensionService;

    public function __construct(
        AccountDimensionItemRepository $accountDimensionItemRepository,
        UserAccountDimensionItemRepository $userAccountDimensionItemRepository,
        AccountDimensionService $accountDimensionService
    ) {
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->userAccountDimensionItemRepository = $userAccountDimensionItemRepository;
        $this->accountDimensionService = $accountDimensionService;
    }
    
    public function getAvailableForAssignment(int $instanceId, ?int $companyId = null): Collection
    {
        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $instanceId,
            $companyId,
            [
                AccountDimensionVisibilityEnum::GENERAL(),
                AccountDimensionVisibilityEnum::REQUEST_HEADER(),
                AccountDimensionVisibilityEnum::DOCUMENT_HEADER(),
            ]
        );

        return $accountDimensions->map(function(AccountDimension $accountDimension) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension
            );
        });
    }

    /**
     * Get user account dimensions with relations
     *
     * @param int $userId
     * @return Collection
     */
    public function getUserAccountDimensions(int $userId): Collection
    {
        return $this->userAccountDimensionItemRepository
            ->findAllForUser($userId)
            ->load(['accountDimension', 'accountDimensionItem']);
    }

    /**
     * Update user account dimensions
     *
     * @param int $userId
     * @param int $instanceId
     * @param int|null $companyId
     * @param Collection $accountDimensions
     * @return void
     * @throws \Throwable
     */
    public function updateUserAccountDimensions(int $userId, int $instanceId, ?int $companyId, Collection $accountDimensions): void
    {
        $this->userAccountDimensionItemRepository->disconnectAll($userId);

        foreach ($accountDimensions as $accountDimensionViewObject) {
            $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
                $accountDimensionViewObject->getAccountDimensionId(),
                $accountDimensionViewObject->getAccountDimensionItemId(),
                $instanceId,
                $companyId
            );

            if ($accountDimensionItem === null) {
                continue;
            }

            $this->userAccountDimensionItemRepository->connectWithItem(
                $userId,
                $accountDimensionItem,
                null,
                $accountDimensionViewObject->getType()
            );
        }
    }

    /**
     * @param UserDto $userDto
     * @param Collection<AccountDimensionItem> $accountDimensionItems
     * @return void
     * @throws \Throwable
     */
    public function connectCollection(UserDto $userDto, Collection $accountDimensionItems): void
    {
        $this->userAccountDimensionItemRepository->disconnectAll($userDto->getId());

        /** @var AccountDimensionItemViewObject $accountDimensionItemViewObject */
        foreach ($accountDimensionItems as $accountDimensionItemViewObject) {
            $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
                $accountDimensionItemViewObject->getAccountDimensionId(),
                $accountDimensionItemViewObject->getId(),
                $userDto->getInstanceId(),
                $userDto->getCompanyId()
            );

            if ($accountDimensionItem === null) {
                continue;
            }

            $this->userAccountDimensionItemRepository->connectWithItem(
                $userDto->getId(),
                $accountDimensionItem,
                null,
                $accountDimensionItemViewObject->getType()
            );
        }
    }
}
