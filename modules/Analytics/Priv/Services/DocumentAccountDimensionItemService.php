<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\DocumentAccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\DocumentAccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\DocumentDto;
use Modules\Analytics\Pub\Dtos\RequestDto;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class DocumentAccountDimensionItemService
{
    protected AccountDimensionService $accountDimensionService;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository;

    protected DocumentAccountDimensionItemRepository $documentAccountDimensionItemRepository;

    public function __construct(
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemRepository $accountDimensionItemRepository,
        RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository,
        DocumentAccountDimensionItemRepository $documentAccountDimensionItemRepository
    ) {
        $this->accountDimensionService = $accountDimensionService;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->requestAccountDimensionItemRepository = $requestAccountDimensionItemRepository;
        $this->documentAccountDimensionItemRepository = $documentAccountDimensionItemRepository;
    }

    public function getDefaults(DocumentDto $documentDto): Collection
    {
        return $this
            ->requestAccountDimensionItemRepository
            ->findByRequestId($documentDto->getRequestId())
            ->map(function (RequestAccountDimensionItem $requestAccountDimensionItem) {
                return $requestAccountDimensionItem->accountDimensionItem;
            });
    }

    public function getAccountingDefaults(DocumentDto $documentDto): Collection
    {
        $defaultDocumentHeaderDimensions = $this
            ->accountDimensionService
            ->findAllActiveForCompany(
                $documentDto->getInstanceId(),
                $documentDto->getCompanyId(),
                [AccountDimensionVisibilityEnum::DOCUMENT_HEADER()]
            )->filter(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem !== null;
            })->map(function(AccountDimension $accountDimension) {
                return $accountDimension->defaultItem;
            });

        return $defaultDocumentHeaderDimensions;
    }

    /**
     * @param DocumentDto $documentDto
     * @param Collection<AccountDimensionItem> $accountDimensionItems
     * @return void
     */
    public function connectCollection(DocumentDto $documentDto, Collection $accountDimensionItems): void
    {
        /** @var AccountDimensionItem $accountDimensionItem */
        foreach ($accountDimensionItems as $accountDimensionItem) {
            $this->documentAccountDimensionItemRepository->connectWithItem($documentDto->getId(), $documentDto->getRequestId(), $accountDimensionItem);
        }
    }

    public function disconnectAll(DocumentDto $documentDto): void
    {
        $this->documentAccountDimensionItemRepository->disconnectAll($documentDto->getId());
    }

    public function connect(DocumentDto $documentDto, AccountDimensionItemViewObject $accountDimensionItemViewObject): DocumentAccountDimensionItem
    {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionItemViewObject->getAccountDimensionId(),
            $accountDimensionItemViewObject->getId(),
            $documentDto->getInstanceId(),
            $documentDto->getCompanyId()
        );

        return $this->documentAccountDimensionItemRepository->connectWithItem($documentDto->getId(), $documentDto->getRequestId(), $accountDimensionItem);
    }

    public function disconnectOne(DocumentDto $documentDto, int $dadId): bool
    {
        $documentAccountDimensionItem = $this->documentAccountDimensionItemRepository->findByDocumentAndId($documentDto->getId(), $dadId);

        abort_if($documentAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));

        return $this->documentAccountDimensionItemRepository->disconnect($documentAccountDimensionItem);
    }

    public function getAvailableForHeader(DocumentDto $documentDto): Collection
    {
        if($documentDto->isRequestSettled()) {
            $visibilites = [
                AccountDimensionVisibilityEnum::DOCUMENT_HEADER(),
            ];
        } else {
            $visibilites = [
                AccountDimensionVisibilityEnum::GENERAL(),
            ];
        }

        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $documentDto->getInstanceId(),
            $documentDto->getCompanyId(),
            $visibilites
        );

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        return $accountDimensions->map(function(AccountDimension $accountDimension) use ($documentDto) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension
            );
        });
    }

    public function getAvailableForAccounting(DocumentDto $documentDto): Collection
    {
        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany($documentDto->getInstanceId(), $documentDto->getCompanyId(), [
            AccountDimensionVisibilityEnum::GENERAL(),
            AccountDimensionVisibilityEnum::ACCOUNTING(),
        ]);

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        return $accountDimensions->map(function(AccountDimension $accountDimension) use ($documentDto) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension
            );
        });
    }

    public function getValue(DocumentDto $documentDto, int $accountDimensionId): ?DocumentAccountDimensionItem
    {
        return $this->documentAccountDimensionItemRepository->findByDocumentAndAccountDimensionId(
            $documentDto->getId(),
            $accountDimensionId
        );
    }

    public function connectWithAllRequestDocuments(
        Collection $documentDtoCollection,
        AccountDimensionItem $accountDimensionItem
    ): void {
        /** @var DocumentDto $documentDto */
        foreach($documentDtoCollection as $documentDto) {
            $this->documentAccountDimensionItemRepository->connectWithItem(
                $documentDto->getId(),
                $documentDto->getRequestId(),
                $accountDimensionItem
            );
        }
    }
}
