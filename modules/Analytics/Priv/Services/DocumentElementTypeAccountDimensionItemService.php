<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Http\Response;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\DocumentElementTypeAccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\DocumentElementTypeAccountDimensionItemRepository;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class DocumentElementTypeAccountDimensionItemService
{
    protected AccountDimensionService $accountDimensionService;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected DocumentElementTypeAccountDimensionItemRepository $documentElementTypeAccountDimensionItemRepository;

    public function __construct(
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemRepository $accountDimensionItemRepository,
        DocumentElementTypeAccountDimensionItemRepository $documentElementTypeAccountDimensionRepository
    ) {
        $this->accountDimensionService = $accountDimensionService;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->documentElementTypeAccountDimensionItemRepository = $documentElementTypeAccountDimensionRepository;
    }

    public function getAvailableForAssignment(int $instanceId)
    {
        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $instanceId,
            null,
            [
                AccountDimensionVisibilityEnum::ACCOUNTING(),
                AccountDimensionVisibilityEnum::GENERAL()
            ]
        );

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        return $accountDimensions->map(function(AccountDimension $accountDimension) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension
            );
        });
    }

    public function connect(
        int $documentElementTypeId,
        AccountDimensionItem $accountDimensionItem
    ): DocumentElementTypeAccountDimensionItem {
        return $this->documentElementTypeAccountDimensionItemRepository->connectWithItem($documentElementTypeId, $accountDimensionItem);
    }

    public function disconnectOne(int $documentElementTypeId, int $detadiId): bool
    {
        $documentElementTypeAccountDimensionItem = $this->documentElementTypeAccountDimensionItemRepository->findByDocumentElementTypeIdAndId(
            $documentElementTypeId,
            $detadiId
        );

        abort_if($documentElementTypeAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));

        return $this->documentElementTypeAccountDimensionItemRepository->disconnect($documentElementTypeAccountDimensionItem);
    }

    public function disconnectAll(int $documentElementTypeId): void
    {
        $this->documentElementTypeAccountDimensionItemRepository->disconnectAll($documentElementTypeId);
    }
}
