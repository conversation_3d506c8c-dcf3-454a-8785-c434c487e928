<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountingAccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountingAccountDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\RequestDto;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class RequestAllowanceAccountingAccountDimensionItemService
{
    protected AccountDimensionService $accountDimensionService;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected RequestAccountingAccountDimensionItemRepository $requestAccountingAccountDimensionItemRepository;

    public function __construct(
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemRepository $accountDimensionItemRepository,
        RequestAccountingAccountDimensionItemRepository $requestAccountingAccountDimensionItemRepository
    ) {
        $this->accountDimensionService = $accountDimensionService;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->requestAccountingAccountDimensionItemRepository = $requestAccountingAccountDimensionItemRepository;
    }

    public function getAvailableForAccountingHeader(RequestDto $requestDto): Collection
    {
        $visibilites = [
            AccountDimensionVisibilityEnum::DOCUMENT_HEADER(),
        ];

        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $requestDto->getInstanceId(),
            $requestDto->getCompanyId(),
            $visibilites
        );

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        return $accountDimensions->map(function(AccountDimension $accountDimension) use ($requestDto) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension
            );
        });
    }

    public function getAccountingDefaults(RequestDto $requestDto): Collection
    {
        $defaultRequestHeaderDimensions = $this
            ->accountDimensionService
            ->findAllActiveForCompany(
                $requestDto->getInstanceId(),
                $requestDto->getCompanyId(),
                [AccountDimensionVisibilityEnum::DOCUMENT_HEADER()]
            )->filter(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem !== null;
            })->map(function(AccountDimension $accountDimension) {
                return $accountDimension->defaultItem;
            });

        return $defaultRequestHeaderDimensions;
    }

    /**
     * @param RequestDto $requestDto
     * @param Collection<AccountDimensionItem> $accountDimensionItems
     * @return void
     */
    public function connectCollection(RequestDto $requestDto, Collection $accountDimensionItems): void
    {
        /** @var AccountDimensionItem $accountDimensionItem */
        foreach ($accountDimensionItems as $accountDimensionItem) {
            $this->requestAccountingAccountDimensionItemRepository->connectWithItem($requestDto->getId(), $accountDimensionItem);
        }
    }

    public function getValue(RequestDto $requestDto, int $accountDimensionId): ?RequestAccountingAccountDimensionItem
    {
        return $this->requestAccountingAccountDimensionItemRepository->findByRequestIdAndAccountDimensionId(
            $requestDto->getId(),
            $accountDimensionId
        );
    }

    public function connect(
        RequestDto $requestDto,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountingAccountDimensionItem {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionItemViewObject->getAccountDimensionId(),
            $accountDimensionItemViewObject->getId(),
            $requestDto->getInstanceId(),
            $requestDto->getCompanyId()
        );

        $requestAccountingAccountDimensionItem = $this->requestAccountingAccountDimensionItemRepository->connectWithItem(
            $requestDto->getId(),
            $accountDimensionItem
        );

        return $requestAccountingAccountDimensionItem;
    }

    public function disconnectOne(RequestDto $requestDto, int $raadiId): bool
    {
        $requestAccountingAccountDimensionItem = $this->requestAccountingAccountDimensionItemRepository->findByRequestAndId($requestDto->getId(), $raadiId);

        abort_if($requestAccountingAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
        abort_if($requestAccountingAccountDimensionItem->accountDimension->is_required === true, Response::HTTP_UNPROCESSABLE_ENTITY, trans('validation.filled'));

        return $this->requestAccountingAccountDimensionItemRepository->disconnect($requestAccountingAccountDimensionItem);
    }
}
