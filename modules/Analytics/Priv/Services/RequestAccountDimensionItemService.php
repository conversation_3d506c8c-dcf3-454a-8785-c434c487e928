<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Accounting\Pub\ViewObjects\AccountDimensionItemViewObject;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\RequestAccountDimensionItem;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Modules\Analytics\Priv\Repositories\AccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\RequestAccountDimensionItemRepository;
use Modules\Analytics\Priv\Repositories\UserAccountDimensionItemRepository;
use Modules\Analytics\Pub\Dtos\RequestDto;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class RequestAccountDimensionItemService
{
    protected AccountDimensionService $accountDimensionService;

    protected AccountDimensionItemRepository $accountDimensionItemRepository;

    protected RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository;

    protected DocumentAccountDimensionItemService $documentAccountDimensionItemService;

    protected UserAccountDimensionItemRepository $userAccountDimensionItemRepository;

    public function __construct(
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemRepository $accountDimensionItemRepository,
        RequestAccountDimensionItemRepository $requestAccountDimensionItemRepository,
        DocumentAccountDimensionItemService $documentAccountDimensionItemService,
        UserAccountDimensionItemRepository $userAccountDimensionItemRepository
    ) {
        $this->accountDimensionService = $accountDimensionService;
        $this->accountDimensionItemRepository = $accountDimensionItemRepository;
        $this->requestAccountDimensionItemRepository = $requestAccountDimensionItemRepository;
        $this->documentAccountDimensionItemService = $documentAccountDimensionItemService;
        $this->userAccountDimensionItemRepository = $userAccountDimensionItemRepository;
    }

    public function getDefaults(RequestDto $requestDto): Collection
    {
        $defaultAccountDimensionItems = $this->accountDimensionService
            ->findAllActiveForCompany(
                $requestDto->getInstanceId(),
                $requestDto->getCompanyId(),
                [AccountDimensionVisibilityEnum::GENERAL()],
            )
            ->filter(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem !== null;
            })->map(function (AccountDimension $accountDimension) {
                return $accountDimension->defaultItem;
            });

        $accountDimensionItems = $this->accountDimensionItemRepository->findActiveByInstancesAndRequestType(
            $requestDto->getInstanceId(),
            $requestDto->getRequestType()
        );
        if (!$accountDimensionItems->isEmpty()) {
            $acDimmByRequestType = $accountDimensionItems->keyBy(function (AccountDimensionItem $accountDimensionItem) {
                return $accountDimensionItem->accountDimension->id;
            });

            return $defaultAccountDimensionItems->merge($acDimmByRequestType);
        }

        $userAccountDimensionItems = $this->userAccountDimensionItemRepository
            ->findAllForUser($requestDto->getUserDto()->getId())
            ->map(function (UserAccountDimensionItem $userAccountDimensionItem) {
                return $userAccountDimensionItem->accountDimensionItem;
            })->keyBy(function (AccountDimensionItem $accountDimensionItem) {
                return $accountDimensionItem->accountDimension->id;
            });

        return $defaultAccountDimensionItems->merge($userAccountDimensionItems);
    }

    /**
     * @param RequestDto $requestDto
     * @param Collection<AccountDimensionItem> $accountDimensionItems
     * @return void
     * @throws \Throwable
     */
    public function connectCollection(RequestDto $requestDto, Collection $accountDimensionItems): void
    {
        /** @var AccountDimensionItem $accountDimensionItem */
        foreach ($accountDimensionItems as $accountDimensionItem) {
            $this->requestAccountDimensionItemRepository->connect($requestDto->getId(), $accountDimensionItem);
        }
    }

    public function connect(
        RequestDto $requestDto,
        Collection $documentDtoCollection,
        AccountDimensionItemViewObject $accountDimensionItemViewObject
    ): RequestAccountDimensionItem {
        $accountDimensionItem = $this->accountDimensionItemRepository->findOneByAccountDimensionIdAndAccountDimensionItemId(
            $accountDimensionItemViewObject->getAccountDimensionId(),
            $accountDimensionItemViewObject->getId(),
            $requestDto->getInstanceId(),
            $requestDto->getCompanyId(),
        );

        $requestAccountDimensionItem = $this->requestAccountDimensionItemRepository->connect(
            $requestDto->getId(),
            $accountDimensionItem,
        );

        $this->documentAccountDimensionItemService->connectWithAllRequestDocuments(
            $documentDtoCollection,
            $accountDimensionItem,
        );

        return $requestAccountDimensionItem;
    }

    public function disconnectOne(RequestDto $requestDto, int $radiId): bool
    {
        $requestAccountDimensionItem = $this->requestAccountDimensionItemRepository->findByRequestAndId(
            $requestDto->getId(),
            $radiId,
        );

        abort_if($requestAccountDimensionItem === null, Response::HTTP_NOT_FOUND, trans('global.item-does-not-exists'));
        abort_if(
            $requestAccountDimensionItem->accountDimension->is_required === true,
            Response::HTTP_UNPROCESSABLE_ENTITY,
            trans('validation.filled'),
        );

        return $this->requestAccountDimensionItemRepository->disconnect($requestAccountDimensionItem);
    }

    public function getAvailableForHeader(RequestDto $requestDto): Collection
    {
        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $requestDto->getInstanceId(),
            $requestDto->getCompanyId(),
            [
                AccountDimensionVisibilityEnum::GENERAL(),
                AccountDimensionVisibilityEnum::REQUEST_HEADER(),
            ],
        );

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        $collection = new Collection();

        /** @var AccessibilityService $accessibilityService */
        $accessibilityService = resolve(AccessibilityService::class);

        /** @var AccountDimension $accountDimension */
        foreach ($accountDimensions as $accountDimension) {
            $accessibility = $accessibilityService->isAccessible(
                $accountDimension->getAccessibilityAttribute(),
                $requestDto->getSlug(),
            );

            if (!$accessibility->isVisible()) {
                continue;
            }

            $wrapper = new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension,
                $accessibility->isDisabled(),
            );

            $collection->push($wrapper);
        }

        return $collection;
    }

    public function getAvailableForAccounting(RequestDto $requestDto): Collection
    {
        $accountDimensions = $this->accountDimensionService->findAllActiveForCompany(
            $requestDto->getInstanceId(),
            $requestDto->getCompanyId(),
            [
                AccountDimensionVisibilityEnum::GENERAL(),
                AccountDimensionVisibilityEnum::ACCOUNTING(),
            ],
        );

        // Load items relation for each account dimension
        $accountDimensions->load('items');

        return $accountDimensions->map(function (AccountDimension $accountDimension) {
            return new AccountDimensionWithActiveCompanyItemsWrapper(
                $accountDimension,
            );
        });
    }

    public function getValue(RequestDto $requestDto, int $accountDimensionId): ?RequestAccountDimensionItem
    {
        return $this->requestAccountDimensionItemRepository->findByRequestIdAndAccountDimensionId(
            $requestDto->getId(),
            $accountDimensionId,
        );
    }
}
