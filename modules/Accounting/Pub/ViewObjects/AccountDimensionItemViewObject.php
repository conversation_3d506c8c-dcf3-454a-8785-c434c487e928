<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Enum\ActiveEnum;
use JsonSerializable;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;

class AccountDimensionItemViewObject implements JsonSerializable
{
    protected int $id;

    protected int $accountDimensionId;

    protected string $accountDimensionSlug;

    protected string $slug;

    protected string $code;

    protected ?string $name;

    protected int $order;

    protected ?string $companySlug;

    protected ActiveEnum $isActive;

    protected bool $isDefault;
    private ?string $type;

    public function __construct(
        int $id,
        int $accountDimensionId,
        string $accountDimensionSlug,
        string $slug,
        string $code,
        ?string $name,
        int $order,
        ?string $companySlug,
        ActiveEnum $isActive,
        bool $isDefault,
        ?string $type
    ) {
        $this->id = $id;
        $this->accountDimensionId = $accountDimensionId;
        $this->accountDimensionSlug = $accountDimensionSlug;
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->order = $order;
        $this->companySlug = $companySlug;
        $this->isActive = $isActive;
        $this->isDefault = $isDefault;
        $this->type = $type;
    }

    public static function createFromAccountDimensionItem(
        AccountDimensionItem $accountDimensionItem,
        ?string $type = null
    ): AccountDimensionItemViewObject {
        return new AccountDimensionItemViewObject(
            $accountDimensionItem->id,
            $accountDimensionItem->account_dimension_id,
            $accountDimensionItem->accountDimension->slug,
            $accountDimensionItem->slug,
            $accountDimensionItem->code,
            $accountDimensionItem->name,
            $accountDimensionItem->order,
            $accountDimensionItem->company_id !== null ? $accountDimensionItem->company->slug : null,
            $accountDimensionItem->is_active,
            $accountDimensionItem->is_default,
            $type
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getAccountDimensionId(): int
    {
        return $this->accountDimensionId;
    }

    public function getAccountDimensionSlug(): string
    {
        return $this->accountDimensionSlug;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function getCompanySlug(): ?string
    {
        return $this->companySlug;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }

    public function getIsDefault(): bool
    {
        return $this->isDefault;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function jsonSerialize(): array
    {
        return [
            'id' => $this->id,
            'account_dimension_id' => $this->accountDimensionId,
            'account_dimension_slug' => $this->accountDimensionSlug,
            'slug' => $this->slug,
            'code' => $this->code,
            'name' => $this->name,
            'order' => $this->order,
            'company_slug' => $this->companySlug,
            'is_active' => $this->isActive->getValue(),
            'is_default' => $this->isDefault,
            'type' => $this->type,
        ];
    }
}
