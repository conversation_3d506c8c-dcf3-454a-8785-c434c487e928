<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Enum\ActiveEnum;
use Modules\Accounting\Priv\Wrappers\AccountDimensionWithActiveCompanyItemsWrapper;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class AccountDimensionWithItemsViewObject
{
    protected int $id;

    protected string $slug;

    protected string $code;

    protected string $name;

    protected int $order;

    protected ?string $companySlug;

    protected AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum;

    protected ActiveEnum $isActive;

    protected bool $isRequired;

    private ?bool $disabled;

    protected array $items;

    public function __construct(
        int $id,
        string $slug,
        string $code,
        string $name,
        int $order,
        ?string $companySlug,
        AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum,
        ActiveEnum $isActive,
        bool $isRequired,
        ?bool $disabled = false,
        array $items = []
    ) {
        $this->id = $id;
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->order = $order;
        $this->companySlug = $companySlug;
        $this->accountDimensionVisibilityEnum = $accountDimensionVisibilityEnum;
        $this->isActive = $isActive;
        $this->isRequired = $isRequired;
        $this->disabled = $disabled;
        $this->items = $items;
    }

    public static function createFromAccountDimensionWrapper(
        AccountDimensionWithActiveCompanyItemsWrapper $wrapper
    ): AccountDimensionWithItemsViewObject {
        $accountDimension = $wrapper->getAccountDimension();
        $items = $accountDimension->items->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'slug' => $item->slug,
                'code' => $item->code,
                'is_active' => $item->is_active,
            ];
        })->toArray();

        return new AccountDimensionWithItemsViewObject(
            $accountDimension->id,
            $accountDimension->slug,
            $accountDimension->code,
            $accountDimension->name,
            $accountDimension->order,
            $accountDimension->company ? $accountDimension->company->slug : null,
            $accountDimension->visibility,
            $accountDimension->is_active,
            $accountDimension->is_required,
            $wrapper->isDisabled(),
            $items
        );
    }

    public static function createOptionalFromAccountDimensionWrapper(
        AccountDimensionWithActiveCompanyItemsWrapper $wrapper
    ): AccountDimensionWithItemsViewObject {
        $accountDimension = $wrapper->getAccountDimension();
        $items = $accountDimension->items->map(function ($item) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'slug' => $item->slug,
                'code' => $item->code,
                'is_active' => $item->is_active,
            ];
        })->toArray();

        return new AccountDimensionWithItemsViewObject(
            $accountDimension->id,
            $accountDimension->slug,
            $accountDimension->code,
            $accountDimension->name,
            $accountDimension->order,
            $accountDimension->company ? $accountDimension->company->slug : null,
            $accountDimension->visibility,
            $accountDimension->is_active,
            false,
            $wrapper->isDisabled(),
            $items
        );
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function getCompanySlug(): ?string
    {
        return $this->companySlug;
    }

    public function getAccountDimensionVisibilityEnum(): AccountDimensionVisibilityEnum
    {
        return $this->accountDimensionVisibilityEnum;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }

    public function isRequired(): bool
    {
        return $this->isRequired;
    }

    public function isDisabled(): ?bool
    {
        return $this->disabled;
    }

    public function getItems(): array
    {
        return $this->items;
    }
}
