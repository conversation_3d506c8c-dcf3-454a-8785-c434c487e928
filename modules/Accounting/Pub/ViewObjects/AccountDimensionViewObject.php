<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use App\Enum\ActiveEnum;
use JsonSerializable;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;

class AccountDimensionViewObject implements JsonSerializable
{
    protected int $id;

    protected string $slug;

    protected string $code;

    protected string $name;

    protected int $order;

    protected ?string $companySlug;

    protected AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum;

    protected ActiveEnum $isActive;

    protected bool $isRequired;

    protected ?AccountDimensionItemViewObject $defaultAccountDimensionItem;

    public function __construct(
        int $id,
        string $slug,
        string $code,
        string $name,
        int $order,
        ?string $companySlug,
        AccountDimensionVisibilityEnum $accountDimensionVisibilityEnum,
        ActiveEnum $isActive,
        bool $isRequired,
        ?AccountDimensionItemViewObject $defaultAccountDimensionItem
    ) {
        $this->id = $id;
        $this->slug = $slug;
        $this->code = $code;
        $this->name = $name;
        $this->order = $order;
        $this->companySlug = $companySlug;
        $this->accountDimensionVisibilityEnum = $accountDimensionVisibilityEnum;
        $this->isActive = $isActive;
        $this->isRequired = $isRequired;
        $this->defaultAccountDimensionItem = $defaultAccountDimensionItem;
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function getSlug(): string
    {
        return $this->slug;
    }

    public function getCode(): string
    {
        return $this->code;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getOrder(): int
    {
        return $this->order;
    }

    public function getCompanySlug(): ?string
    {
        return $this->companySlug;
    }

    public function getVisibility(): AccountDimensionVisibilityEnum
    {
        return $this->accountDimensionVisibilityEnum;
    }

    public function getIsActive(): ActiveEnum
    {
        return $this->isActive;
    }

    public static function createFromAccountDimension(AccountDimension $accountDimension): AccountDimensionViewObject
    {
        return new AccountDimensionViewObject(
            $accountDimension->id,
            $accountDimension->slug,
            $accountDimension->code,
            $accountDimension->name,
            $accountDimension->order,
            $accountDimension->company ? $accountDimension->company->slug : null,
            $accountDimension->visibility,
            $accountDimension->is_active,
            $accountDimension->is_required,
            $accountDimension->defaultItem !== null ? AccountDimensionItemViewObject::createFromAccountDimensionItem($accountDimension->defaultItem) : null
        );
    }

    public function isRequired(): bool
    {
        return $this->isRequired;
    }

    public function getDefaultAccountDimensionItem(): ?AccountDimensionItemViewObject
    {
        return $this->defaultAccountDimensionItem;
    }
}