<?php

declare(strict_types=1);

namespace Modules\Accounting\Pub\ViewObjects;

use JsonSerializable;
use Modules\Analytics\Priv\Entities\AccountDimensionItem;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;

class SelectedAccountDimensionViewObject implements JsonSerializable
{
    protected AccountDimensionViewObject $accountDimensionViewObject;

    protected AccountDimensionItemViewObject $accountDimensionItemViewObject;
    private ?string $type;

    public function __construct(
        AccountDimensionViewObject $accountDimensionViewObject,
        AccountDimensionItemViewObject $accountDimensionItemViewObject,
        ?string $type = null
    ) {
        $this->accountDimensionViewObject = $accountDimensionViewObject;
        $this->accountDimensionItemViewObject = $accountDimensionItemViewObject;
        $this->type = $type;
    }

    public function getAccountDimensionViewObject(): AccountDimensionViewObject
    {
        return $this->accountDimensionViewObject;
    }

    public function getAccountDimensionItemViewObject(): AccountDimensionItemViewObject
    {
        return $this->accountDimensionItemViewObject;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public static function createFromAccountDimensionItem(AccountDimensionItem $item): SelectedAccountDimensionViewObject
    {
        return new static(
            AccountDimensionViewObject::createFromAccountDimension($item->accountDimension),
            AccountDimensionItemViewObject::createFromAccountDimensionItem($item)
        );
    }

    public static function createFromUserAccountDimension(UserAccountDimensionItem $userAccountDimensionItem
    ): SelectedAccountDimensionViewObject {
        return new static(
            AccountDimensionViewObject::createFromAccountDimension($userAccountDimensionItem->accountDimension),
            AccountDimensionItemViewObject::createFromAccountDimensionItem(
                $userAccountDimensionItem->accountDimensionItem
            ),
            $userAccountDimensionItem->type,
        );
    }
}
