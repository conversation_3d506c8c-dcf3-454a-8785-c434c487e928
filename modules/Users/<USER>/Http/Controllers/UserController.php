<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Http\Controllers;

use App\Http\Requests\CurrentInstanceRequest;
use App\Http\Responses\AccountDimensionWithActiveCompanyItemsWrapperResponse;
use App\Http\Responses\Response2;
use App\Http\Responses\UserBasicResponse;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Http\JsonResponse;
use Illuminate\Routing\Controller;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Modules\Users\Priv\Factory\NewUserDtoFactory;
use Modules\Users\Priv\Http\Dto\ChangeSensitiveDataDto;
use Modules\Users\Priv\Http\Requests\NewUserRequest;
use Modules\Users\Priv\Http\Requests\UpdateUserAccountDimensionsRequest;
use Modules\Users\Priv\Services\UserService;

class UserController extends Controller
{
    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var UserService
     */
    protected $userService;

    /**
     * @var NewUserDtoFactory
     */
    protected $newUserDtoFactory;

    /**
     * UserController constructor.
     * @param Guard $auth
     * @param UserService $userService
     * @param NewUserDtoFactory $newUserDtoFactory
     */
    public function __construct(Guard $auth, UserService $userService, NewUserDtoFactory $newUserDtoFactory)
    {
        $this->auth = $auth;
        $this->userService = $userService;
        $this->newUserDtoFactory = $newUserDtoFactory;
    }

    public function store(NewUserRequest $request): Responsable
    {
        /** @var User $currentUser */
        $currentUser = $this->auth->user();

        $newUserDto = $this->newUserDtoFactory->create($request);

        $user = $this->userService->create($newUserDto, $currentUser);

        return UserBasicResponse::item($user)->addInfo(trans('user.profile-has-been-saved'));
    }

    public function sensitiveDataUpdate(ChangeSensitiveDataDto $changeSensitiveDataDto): Responsable
    {
        $user = $this->userService->changeSensitiveData($changeSensitiveDataDto);

        return UserBasicResponse::item($user)->addInfo(trans('user.profile-has-been-saved'));
    }

    public function accountDimensions(CurrentInstanceRequest $request, AccountDimensionItemFacade $facade): Responsable
    {
        /** @var User $user */
        $user = $request->user();

        return AccountDimensionWithActiveCompanyItemsWrapperResponse::collection(
            $facade->getAvailableForUsers($user->instance_id, $user->company_id)
        );
    }

    public function getUserAccountDimensions(string $slug, AccountDimensionItemFacade $facade): JsonResponse
    {
        $user = User::where('slug', $slug)->firstOrFail();

        return response()->json($facade->getUserAccountDimensions($user->id));
    }

    public function updateUserAccountDimensions(
        string $slug,
        UpdateUserAccountDimensionsRequest $request,
        AccountDimensionItemFacade $facade
    ): JsonResponse {
        $user = User::where('slug', $slug)->firstOrFail();
        $dto = $request->getDto($user);

        $facade->updateUserAccountDimensions(
            $user->id,
            $user->instance_id,
            $user->company_id,
            $dto->getAccountDimensions()
        );

        return response()->json([
            'message' => trans('user.account-dimensions-updated'),
            'data' => $facade->getUserAccountDimensions($user->id)
        ]);
    }
}
