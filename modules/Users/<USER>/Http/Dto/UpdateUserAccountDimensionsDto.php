<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Http\Dto;

use App\User;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\UserAccountDimensionViewObject;

class UpdateUserAccountDimensionsDto
{
    protected User $user;

    protected Collection $accountDimensions;

    public function __construct(User $user, Collection $accountDimensions)
    {
        $this->user = $user;
        $this->accountDimensions = $accountDimensions;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function getAccountDimensions(): Collection
    {
        return $this->accountDimensions;
    }

    public static function createFromRequest(array $data, User $user): self
    {
        $accountDimensions = collect($data['account_dimensions'] ?? [])
            ->map(function ($dimension) {
                return UserAccountDimensionViewObject::createFromArray($dimension);
            });

        return new self($user, $accountDimensions);
    }
}
