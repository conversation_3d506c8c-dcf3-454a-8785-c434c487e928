<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Http\Requests;

use App\Permission;
use App\Repositories\GroupRepository;
use App\User;
use Carbon\Carbon;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Support\Collection;
use Modules\Accounting\Pub\ViewObjects\UserAccountDimensionViewObject;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Modules\Common\Http\Requests\BaseFormRequest;
use Modules\Users\Priv\Services\GradeService;
use Modules\Users\Pub\Interfaces\NewUserRequestInterface;
use Ramsey\Uuid\Uuid;

class NewUserRequest extends BaseFormRequest implements NewUserRequestInterface
{
    public const KEY_LOCALE = 'locale';

    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var GroupRepository
     */
    protected $groupRepository;

    /**
     * @var GradeService
     */
    protected $gradeService;

    protected AccountDimensionItemFacade $accountDimensionItemFacade;

    /**
     * NewUserDto constructor.
     * @param Guard $auth
     * @param GroupRepository $groupRepository
     * @param GradeService $gradeService
     */
    public function __construct(Guard $auth, GroupRepository $groupRepository, GradeService $gradeService, AccountDimensionItemFacade $accountDimensionItemFacade)
    {
        $this->auth = $auth;
        $this->groupRepository = $groupRepository;
        $this->gradeService = $gradeService;
        $this->accountDimensionItemFacade = $accountDimensionItemFacade;
    }

    public function authorize()
    {
        /** @var User $user */
        $user = $this->auth->user();

        return
            $user->hasAbility(Permission::USER_CREATE) ||
            $user->hasAbility(Permission::INSTANCE_SETTINGS_MANAGE_USERS);
    }

    public function rules(): array
    {
        return [];
    }

    public function getFirstName(): ?string
    {
        return $this->get(NewUserRequestInterface::KEY_FIRST_NAME);
    }

    public function getLastName(): ?string
    {
        return $this->get(NewUserRequestInterface::KEY_LAST_NAME);
    }

    public function getCompanyId(): ?int
    {
        return $this->has(NewUserRequestInterface::KEY_COMPANY_ID) === true
        && empty($this->get(NewUserRequestInterface::KEY_COMPANY_ID)) === false
            ? (int)$this->get(NewUserRequestInterface::KEY_COMPANY_ID)
            : null;
    }

    public function getSupervisorId(): ?int
    {
        return $this->get(NewUserRequestInterface::KEY_SUPERVISOR_ID);
    }

    public function getAssistantId(): ?int
    {
        return $this->get(NewUserRequestInterface::KEY_ASSISTANT_ID);
    }

    public function getEmail(): ?string
    {
        return strtolower($this->get(NewUserRequestInterface::KEY_EMAIL) ?? '');
    }

    public function getSex(): ?string
    {
        return $this->get(NewUserRequestInterface::KEY_SEX);
    }

    public function getGroups(): Collection
    {
        return new Collection(
            $this->get(NewUserRequestInterface::KEY_GROUP_IDS)
        );
    }

    public function getCitizenshipId(): ?int
    {
        return $this->has(NewUserRequestInterface::KEY_CITIZENSHIP_ID) === true
        && empty($this->get(NewUserRequestInterface::KEY_CITIZENSHIP_ID)) === false
            ? (int)$this->get(NewUserRequestInterface::KEY_CITIZENSHIP_ID)
            : null;
    }

    public function getMpkId(): ?int
    {
        return $this->has(NewUserRequestInterface::KEY_MPK_ID) === true
        && empty($this->get(NewUserRequestInterface::KEY_MPK_ID)) === false
            ? (int)$this->get(NewUserRequestInterface::KEY_MPK_ID)
            : null;
    }

    public function getBirthDate(): ?Carbon
    {
        $birthDate = $this->get(NewUserRequestInterface::KEY_BIRTH_DATE);
        if ($birthDate === null) {
            return null;
        }

        return Carbon::createFromFormat('Y-m-d', $birthDate);
    }

    public function getPhoneNumber(): ?string
    {
        return $this->get(NewUserRequestInterface::KEY_PHONE_NUMBER);
    }

    public function getGradeId(): ?int
    {
        return $this->has(NewUserRequestInterface::KEY_GRADE_ID)
            ? (int)$this->get(NewUserRequestInterface::KEY_GRADE_ID)
            : null;
    }

    public function getErpId(): ?string
    {
        return null;
    }

    public function getFirstNameKey(): string
    {
        return NewUserRequestInterface::KEY_FIRST_NAME;
    }

    public function getLastNameKey(): string
    {
        return NewUserRequestInterface::KEY_LAST_NAME;
    }

    public function getCompanyIdKey(): string
    {
        return NewUserRequestInterface::KEY_COMPANY_ID;
    }

    public function getSupervisorIdKey(): string
    {
        return NewUserRequestInterface::KEY_SUPERVISOR_ID;
    }

    public function getAssistantIdKey(): string
    {
        return NewUserRequestInterface::KEY_ASSISTANT_ID;
    }

    public function getEmailKey(): string
    {
        return NewUserRequestInterface::KEY_EMAIL;
    }

    public function getSexKey(): string
    {
        return NewUserRequestInterface::KEY_SEX;
    }

    public function getGroupsKey(): string
    {
        return NewUserRequestInterface::KEY_GROUP_IDS;
    }

    public function getCitizenshipIdKey(): string
    {
        return NewUserRequestInterface::KEY_CITIZENSHIP_ID;
    }

    public function getMpkIdKey(): string
    {
        return NewUserRequestInterface::KEY_MPK_ID;
    }

    public function getBirthDateKey(): string
    {
        return NewUserRequestInterface::KEY_BIRTH_DATE;
    }

    public function getPhoneNumberKey(): string
    {
        return NewUserRequestInterface::KEY_PHONE_NUMBER;
    }

    public function getGradeIdKey(): string
    {
        return NewUserRequestInterface::KEY_GRADE_ID;
    }

    /**
     * @return string key not used in requests from Admin Panel (GU)
     */
    public function getErpIdKey(): string
    {
        return NewUserRequestInterface::KEY_ERP_ID;
    }

    public function getLanguage(): ?string
    {
        return $this->get(static::KEY_LOCALE);
    }

    public function getLanguageKey(): string
    {
        return static::KEY_LOCALE;
    }

    public function getInstanceId(): int
    {
        $instance = $this->auth->user()->instance;
        $instanceId = $instance->id;

        return $instanceId;

    }

    public function getAuthUserId(): int
    {
        return $this->auth->user()->id;
    }

    public function getHrId(): ?string
    {
        return null;
    }

    public function getHrIdKey(): string
    {
        return static::KEY_HR_ID;
    }

    public function getWorkLocation(): ?string
    {
        return null;
    }

    public function getWorkLocationKey(): string
    {
        return static::KEY_WORK_LOCATION;
    }

    public function getAccountDimensions(): Collection
    {
        $accountDimensions = new Collection();
        foreach ($this->get(NewUserRequestInterface::KEY_ACCOUNT_DIMENSIONS, []) as $accountDimension) {
            $item = $this->accountDimensionItemFacade->getById(
                $accountDimension['account_dimension_item_id'],
                $this->user()->instance_id
            );

            $adId = $item->getAccountDimensionId();
            $adItemId = $item->getId();
            $accountDimensions->put(
                $item->getId(),
                new UserAccountDimensionViewObject(
                    $adId,
                    $adItemId,
                    $accountDimension['type'] ?? null
                ),
            );
        }

        return $accountDimensions;
    }

    public function getAccountDimensionsKey(): string
    {
        return NewUserRequestInterface::KEY_ACCOUNT_DIMENSIONS;
    }

    public function getEmployeeUniqueIdentifier(): string
    {
        if($this->has(NewUserRequestInterface::KEY_EMPLOYEE_UNIQUE_IDENTIFIER) === false) {
            return $this->getEmail() ?? (string) Uuid::uuid4();
        }

        return $this->get(NewUserRequestInterface::KEY_EMPLOYEE_UNIQUE_IDENTIFIER, '');
    }

    public function getEmployeeUniqueIdentifierKey(): string
    {
        return NewUserRequestInterface::KEY_EMPLOYEE_UNIQUE_IDENTIFIER;
    }

    public function getSlug(): string
    {
        return $this->get(NewUserRequestInterface::KEY_SLUG) ?: User::generateSlug();
    }

    public function getSlugKey(): string
    {
        return NewUserRequestInterface::KEY_SLUG;
    }

    public function getMpkKey(): string
    {
        return 'mpk_id';
    }

    public function getMpk(): array
    {
        $mpkId = $this->get($this->getMpkIdKey());

        return [[
            'id' => $mpkId,
            'percentage' => 100,
            'main' => 1,
        ]];
    }
}
