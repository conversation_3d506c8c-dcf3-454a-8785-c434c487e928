<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Http\Requests;

use App\Http\Requests\CurrentInstanceRequest;
use App\User;
use Modules\Analytics\Priv\Entities\UserAccountDimensionItem;
use Modules\Users\Priv\Http\Dto\UpdateUserAccountDimensionsDto;

class UpdateUserAccountDimensionsRequest extends CurrentInstanceRequest
{
    public function rules(): array
    {
        return [
            'account_dimensions' => 'array',
            'account_dimensions.*.account_dimension_id' => 'required|integer|exists:account_dimensions,id',
            'account_dimensions.*.account_dimension_item_id' => 'required|integer|exists:account_dimension_items,id',
            'account_dimensions.*.type' => 'required|string|in:' . implode(',', UserAccountDimensionItem::getAllowedTypes()),
        ];
    }

    public function messages(): array
    {
        return [
            'account_dimensions.*.account_dimension_id.required' => trans('validation.required'),
            'account_dimensions.*.account_dimension_id.integer' => trans('validation.integer'),
            'account_dimensions.*.account_dimension_id.exists' => trans('validation.exists'),
            'account_dimensions.*.account_dimension_item_id.required' => trans('validation.required'),
            'account_dimensions.*.account_dimension_item_id.integer' => trans('validation.integer'),
            'account_dimensions.*.account_dimension_item_id.exists' => trans('validation.exists'),
            'account_dimensions.*.type.required' => trans('validation.required'),
            'account_dimensions.*.type.string' => trans('validation.string'),
            'account_dimensions.*.type.in' => trans('validation.in'),
        ];
    }

    public function getDto(User $user): UpdateUserAccountDimensionsDto
    {
        return UpdateUserAccountDimensionsDto::createFromRequest($this->validated(), $user);
    }
}
