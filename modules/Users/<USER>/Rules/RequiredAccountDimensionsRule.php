<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Rules;

use App\Company;
use App\Instance;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Modules\Accounting\Pub\ViewObjects\UserAccountDimensionViewObject;

class RequiredAccountDimensionsRule implements Rule
{
    protected AccountDimensionService $accountDimensionService;
    protected Instance $instance;
    protected ?Company $company;
    protected Collection $providedAccountDimensions;
    protected array $missingRequiredDimensions = [];

    public function __construct(AccountDimensionService $accountDimensionService)
    {
        $this->accountDimensionService = $accountDimensionService;
    }

    public function setInstance(Instance $instance): self
    {
        $this->instance = $instance;
        return $this;
    }

    public function setCompany(?Company $company): self
    {
        $this->company = $company;
        return $this;
    }

    public function setProvidedAccountDimensions(Collection $providedAccountDimensions): self
    {
        $this->providedAccountDimensions = $providedAccountDimensions;
        return $this;
    }

    public function passes($attribute, $value)
    {
        // Pobierz wszystkie wymiary analityczne dla instancji
        $allAccountDimensions = $this->accountDimensionService->findAllActiveForInstance($this->instance);
        
        // Filtruj tylko wymagane wymiary
        $requiredDimensions = $allAccountDimensions->filter(function ($dimension) {
            return $dimension->is_required;
        });

        // Sprawdź czy wszystkie wymagane wymiary zostały podane
        foreach ($requiredDimensions as $requiredDimension) {
            $found = $this->providedAccountDimensions->contains(function ($providedDimension) use ($requiredDimension) {
                // Sprawdź czy providedDimension to UserAccountDimensionViewObject
                if ($providedDimension instanceof UserAccountDimensionViewObject) {
                    return $providedDimension->getAccountDimensionId() === $requiredDimension->id;
                }

                // Fallback dla array
                return isset($providedDimension['account_dimension_id'])
                    && $providedDimension['account_dimension_id'] === $requiredDimension->id
                    && !empty($providedDimension['account_dimension_item_id']);
            });

            if (!$found) {
                $this->missingRequiredDimensions[] = $requiredDimension->name;
            }
        }

        return empty($this->missingRequiredDimensions);
    }

    public function message()
    {
        if (empty($this->missingRequiredDimensions)) {
            return trans('users::error.required-account-dimensions-missing');
        }

        $dimensionsList = implode(', ', $this->missingRequiredDimensions);

        return trans('users::error.required-account-dimensions-missing-with-names', [
            'dimensions' => $dimensionsList
        ]);
    }
}
