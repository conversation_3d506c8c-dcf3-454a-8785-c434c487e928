<?php

return [
	'security' => 'Security',
	'personal-data-header' => 'personal data',
	'personal-data' => 'Personal data',
	'first-name' => 'First name',
	'last-name' => 'Last name',
	'birth-date' => 'Birth date',
	'phone' => 'Phone',
	'phone-ice' => 'ICE Phone',
	'passport-number' => 'Passport number',
	'passport-issue-date' => 'Passport issue date',
	'passport-valid-date' => 'Passport valid date',
	'identity-card-number' => 'Identity card number',
	'nationality' => 'Nationality',
	'citizenship' => 'Citizenship',
    'add-card' => 'Add card',
    'card-number' => 'Card number',
    'card-type' => 'Card type',
	'card-company' => 'Company',
    'operations' => 'Operations',
    'default-card' => 'Default card',
    'set-as-default' => 'Set as default',
	'change-password' => 'Change password',
	'change-pin' => 'Change PIN code',
	'current_password' => 'Old password',
	'current_pin' => 'Old PIN code',
	'new_password' => 'New password',
	'new_pin' => 'New PIN code',
	'new_password_confirmation' => 'New password confirmation',
	'new_pin_confirmation' => 'New PIN code confirmation',
	'select-country' => 'Select country',
	'page-title' => 'Profile',
	'profile-has-been-saved' => 'Profile has been saved',
	'select-language' => 'Select language',
	'language' => 'Language',
	'assistants' => 'Assistance to',
	'substitutions' => 'Substitutions',
	'substitute' => 'My substitute',
	'charge-cards' => 'Payment cards',
	'delete' => 'Delete',
	'from' => 'From',
	'to' => 'To',
	'personal' => 'Personal',
	'set-substitution' => 'substitution',
	'set' => 'Set',
	'payment-cards-adding' => 'Adding...',
	'card-type-individual' => 'User',
	'card-type-corpo' => 'Corporate',
	'work-location' => 'Work location',
	'company-hierarchy' => 'Company structure',
	'back-to' => 'Powrót do...',
    'users' => 'Users',
	'set-assistant' => 'Set assistant',
	'assistants-list' => 'Assistants',
	'find-user' => 'Type to find the user or assistant',
	'priority' => 'Priority',
	'priority-tooltip' => 'Payments are processed in correspondence to card priority. In order to change payment card drag & drop it to higher priority.',
    'license_plate' => 'Private car registration number',
    'company_license_plate' => 'Company car registration number',
    'iban' => 'Bank account number',
	'sex' => 'Sex',
	'select-sex' => 'Select sex',
	'male'   => 'Male',
	'female' => 'Female',
    'bad-pin-code' => 'Invalid PIN code',
    'pin-code-locked' => 'PIN code is blocked. Please create new one in User profile',
    'no-results-for-search-message' => 'No results',
    'searching-message' => 'Searching...',
    'send-welcome-email' => 'Send welcome email again',
    'account-dimensions-title' => 'Account dimensions',
    'account-dimensions-updated' => 'Account dimensions have been updated',

    'email' => 'Email',
    'company' => [
        'name' => 'Company'
    ]
];
