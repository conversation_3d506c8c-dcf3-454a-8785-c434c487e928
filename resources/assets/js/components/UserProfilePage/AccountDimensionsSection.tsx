import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { SelectField } from '../ui/Form'
import { trans } from '../../trans'
import { Loader } from '../ui/LoadingOverlay/Loader'
import { AccountDimensionFormItem } from '../../types/account-dimension'

interface UserAccountDimension {
  accountDimensionViewObject: {
    id: number
    slug: string
    name: string
  }
  accountDimensionItemViewObject: {
    id: number
    code: string
    name: string
    accountDimensionId: number
  }
  type: string
}

interface AccountDimensionsSectionProps {
  userSlug: string
  accountDimensions: AccountDimensionFormItem[]
  userAccountDimensions: UserAccountDimension[]
  isLoaded: boolean
  onUpdate: (dimensions: any[]) => void
}

interface AccountDimensionsSectionState {
  dimensions: { [key: string]: { value: number | null; type: string } }
  isUpdating: boolean
}

const TYPE_OPTIONS = [
  { value: 'DEFAULT', label: trans('account-dimensions.type.default') },
  { value: 'SUGGESTION', label: trans('account-dimensions.type.suggestion') },
  { value: 'OWNER', label: trans('account-dimensions.type.owner') },
]

export default class AccountDimensionsSection extends Component<
  AccountDimensionsSectionProps,
  AccountDimensionsSectionState
> {
  constructor(props: AccountDimensionsSectionProps) {
    super(props)

    this.state = {
      dimensions: {},
      isUpdating: false,
    }
  }

  componentDidMount() {
    this.initializeDimensions()
  }

  componentDidUpdate(prevProps: AccountDimensionsSectionProps) {
    if (
      prevProps.userAccountDimensions !== this.props.userAccountDimensions ||
      prevProps.accountDimensions !== this.props.accountDimensions
    ) {
      this.initializeDimensions()
    }
  }

  initializeDimensions = () => {
    const { accountDimensions, userAccountDimensions } = this.props
    const dimensions: { [key: string]: { value: number | null; type: string } } = {}

    // Check if accountDimensions is available
    if (!accountDimensions || !Array.isArray(accountDimensions)) {
      return
    }

    // Initialize all available dimensions with null values
    accountDimensions.forEach((dimension) => {
      dimensions[dimension.slug] = {
        value: null,
        type: 'DEFAULT',
      }
    })

    // Set values from user's assigned dimensions
    if (userAccountDimensions && Array.isArray(userAccountDimensions)) {
      console.log('TEST1', userAccountDimensions)
      userAccountDimensions.forEach((userDimension) => {
        console.log('TEST2', userDimension)
        const dimensionSlug = userDimension.accountDimensionViewObject.slug
        if (dimensions[dimensionSlug] !== undefined) {
          dimensions[dimensionSlug] = {
            value: userDimension.accountDimensionItemViewObject.id,
            type: userDimension.type || 'DEFAULT',
          }
        }
      })
    }

    this.setState({ dimensions })
  }

  handleDimensionChange = (dimensionSlug: string, value: number | null) => {
    this.setState((prevState) => ({
      dimensions: {
        ...prevState.dimensions,
        [dimensionSlug]: {
          ...prevState.dimensions[dimensionSlug],
          value,
        },
      },
    }))
  }

  handleTypeChange = (dimensionSlug: string, type: string) => {
    this.setState((prevState) => ({
      dimensions: {
        ...prevState.dimensions,
        [dimensionSlug]: {
          ...prevState.dimensions[dimensionSlug],
          type,
        },
      },
    }))
  }

  handleSave = () => {
    const { dimensions } = this.state
    const { accountDimensions, onUpdate } = this.props

    // Check if accountDimensions is available
    if (!accountDimensions || !Array.isArray(accountDimensions)) {
      return
    }

    // Convert to API format
    const dimensionsToSave = accountDimensions
      .map((dimension) => {
        const userDimension = dimensions[dimension.slug]
        if (userDimension && userDimension.value !== null) {
          return {
            account_dimension_id: dimension.id,
            account_dimension_item_id: userDimension.value,
            type: userDimension.type,
          }
        }
        return null
      })
      .filter(Boolean)

    this.setState({ isUpdating: true })
    onUpdate(dimensionsToSave)
      .finally(() => {
        this.setState({ isUpdating: false })
      })
  }

  render() {
    const { accountDimensions, isLoaded } = this.props
    const { dimensions, isUpdating } = this.state

    if (!isLoaded) {
      return <Loader />
    }

    if (!accountDimensions || !Array.isArray(accountDimensions)) {
      return (
        <div className="account-dimensions-section">
          <h1>{trans('settings-page.account-dimensions')}</h1>
          <p>{trans('account-dimensions.no-dimensions-available')}</p>
        </div>
      )
    }

    return (
      <div className="account-dimensions-section">
        <h1>{trans('settings-page.account-dimensions')}</h1>

        {accountDimensions.map((dimension) => {
          const userDimension = dimensions[dimension.slug] || { value: null, type: 'DEFAULT' }

          return (
            <div key={dimension.id} className="account-dimension-row">
              <div className="form-group form-group--label-top">
                <span className="form-group__label">{dimension.label}:</span>
                <div className="form-group__input-wrapper">
                  <div className="account-dimension-controls">
                    <div className="account-dimension-item">
                      <SelectField
                        name={`dimension_${dimension.slug}`}
                        value={userDimension.value}
                        onChange={(value) => this.handleDimensionChange(dimension.slug, value)}
                        options={dimension.options}
                        placeholder={trans('account-dimensions.select-item')}
                        disabled={isUpdating}
                      />
                    </div>
                    <div className="account-dimension-type">
                      <SelectField
                        name={`type_${dimension.slug}`}
                        value={userDimension.type}
                        onChange={(type) => this.handleTypeChange(dimension.slug, type)}
                        options={TYPE_OPTIONS}
                        disabled={isUpdating || userDimension.value === null}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}

        <div className="form-group">
          <button
            type="button"
            className="btn btn--primary"
            onClick={this.handleSave}
            disabled={isUpdating}
          >
            {isUpdating ? trans('global.saving') : trans('global.save')}
          </button>
        </div>

        {isUpdating && <Loader />}
      </div>
    )
  }
}

AccountDimensionsSection.propTypes = {
  userSlug: PropTypes.string.isRequired,
  accountDimensions: PropTypes.array.isRequired,
  userAccountDimensions: PropTypes.array.isRequired,
  isLoaded: PropTypes.bool.isRequired,
  onUpdate: PropTypes.func.isRequired,
}
