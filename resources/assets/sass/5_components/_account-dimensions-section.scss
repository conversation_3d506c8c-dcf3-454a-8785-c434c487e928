.account-dimensions-section {
  margin: 20px 0;
  
  h1 {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 500;
    color: $color-grey-06;
  }
}

.account-dimension-row {
  margin-bottom: 15px;
  
  .form-group {
    margin-bottom: 0;
  }
}

.account-dimension-controls {
  display: flex;
  gap: 10px;
  width: 100%;
  
  .account-dimension-item {
    flex: 2;
    min-width: 200px;
  }
  
  .account-dimension-type {
    flex: 1;
    min-width: 120px;
  }
  
  @include rwd(small) {
    flex-direction: column;
    gap: 5px;
    
    .account-dimension-item,
    .account-dimension-type {
      flex: none;
      width: 100%;
      min-width: auto;
    }
  }
}

.account-dimensions-section {
  .form-group__label {
    font-weight: 500;
    color: $color-grey-06;
  }
  
  .btn {
    margin-top: 15px;
  }
  
  .has-loader {
    position: relative;
  }
}
